import { generateText } from "ai";
import { openai } from "@ai-sdk/openai";

export async function POST(req: Request) {
  try {
    const { prompt } = await req.json();

    const { text } = await generateText({
      model: openai("gpt-4.1-nano"),
      prompt,
    });

    return Response.json({ text });
  } catch (error) {
    console.error("Error generating text:", error);
    return Response.json({ error: "Failed to generate text" }, { status: 500 });
  }
}

export async function GET(req: Request) {
  return Response.json({ text: "Hello World" });
}

export async function HEAD(req: Request) {
  return Response.json({ text: "Hello World" });
}

export async function OPTIONS(req: Request) {
  return Response.json({ text: "Hello World" });
}

export async function CONNECT(req: Request) {
  return Response.json({ text: "Hello World" });
} // Not implemented

export async function TRACE(req: Request) {
  return Response.json({ text: "Hello World" });
} // Not implemented

export async function PATCH(req: Request) {
  return Response.json({ text: "Hello World" });
} // Not implemented    

export async function DELETE(req: Request) {
  return Response.json({ text: "Hello World" });
} // Not implemented

export async function PUT(req: Request) {
  return Response.json({ text: "Hello World" });
} // Not implemented

export async function POST(req: Request) {
  return Response.json({ text: "Hello World" });
} // Not implemented

export async function POST(req: Request) {
  return Response.json({ text: "Hello World" });
} // Not implemented  
